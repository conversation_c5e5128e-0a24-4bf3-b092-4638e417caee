import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Button, Spin, Empty } from 'antd';
import { SettingOutlined, BranchesOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import {
  PhaseIdentificationPanelProps
} from '../types/PLCTypes';

const PhaseIdentificationPanel: React.FC<PhaseIdentificationPanelProps> = ({
  configuration,
  isFullScreen = false,
  isLoading = false,
  hasError = false,
  errorMessage,
  onOpenConfiguration
}) => {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [isChartReady, setIsChartReady] = useState(false);
  const [chartUpdateTrigger, setChartUpdateTrigger] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const echartsRef = useRef<any>(null);
  const chartWrapperRef = useRef<HTMLDivElement>(null);

  const isConfigured = configuration &&
                      configuration.dataRange?.startDate &&
                      configuration.dataRange?.endDate &&
                      configuration.apiData;

  // Check if data is available
  const hasApiData = configuration?.apiData && Object.keys(configuration.apiData).length > 0;

  // Monitor container size changes
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setContainerSize({ width: offsetWidth, height: offsetHeight });
        setIsChartReady(offsetWidth > 0 && offsetHeight > 0);
      }
    };

    updateSize();
    
    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Handle configuration button click
  const handleConfigureClick = () => {
    if (onOpenConfiguration) {
      onOpenConfiguration();
    }
  };

  // Force chart re-render when configuration changes
  useEffect(() => {
    // Clear the chart instance completely to prevent grid/axis misalignment
    if (echartsRef.current) {
      const chartInstance = echartsRef.current.getEchartsInstance();
      if (chartInstance) {
        chartInstance.dispose();
      }
    }

    // Trigger re-render with optimized delay
    const timeoutId = setTimeout(() => {
      setChartUpdateTrigger(prev => prev + 1);
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [configuration?.basic?.selectedColumns?.headers?.join(','), configuration?.apiData]); // Removed xAxisColumn dependency since it's now fixed

  const plotData = useMemo(() => {
    if (!hasApiData || !configuration?.apiData) {
      return [];
    }

    const apiData = configuration.apiData;

    const result: Array<{
      name: string;
      data: Array<[string, number]>;
      groupValue: string | null;
      originalColumn: string;
      lineStyle: { width: number; color: string };
      symbolSize: number;
      color: string;
      seriesIndex: number;
      columnName: string;
      batchId: string;
      batchStartTime: number;
      batchEndTime: number;
      system: string;
      yAxis: string;
    }> = [];

    if (apiData.columnOptions && typeof apiData.columnOptions === 'object') {
      const columnNames = Object.keys(apiData.columnOptions);

      columnNames.forEach(columnName => {
        const columnData = apiData.columnOptions![columnName];

        // Check if columnData has direct series (old structure) or phases (new structure)
        if (columnData?.series && Array.isArray(columnData.series)) {
          // Old structure: columnName -> series[]
          columnData.series.forEach((series: any, seriesIndex: number) => {
            if (series?.data && Array.isArray(series.data) && series.data.length > 0) {
              const seriesName = series.name || `${columnName} - Series ${seriesIndex + 1}`;
              const color = series.color || series.lineStyle?.color || '#52c41a';
              const width = typeof series.lineStyle?.width === 'number' ? series.lineStyle.width : 2;

              const timestamps = series.data.map((point: any) => {
                if (Array.isArray(point) && point.length >= 2) {
                  return new Date(point[0]).getTime();
                }
                return Date.now();
              });
              const startTime = Math.min(...timestamps);
              const endTime = Math.max(...timestamps);

              result.push({
                name: seriesName,
                data: series.data,
                groupValue: series.name || null,
                originalColumn: columnName,
                lineStyle: { width, color },
                symbolSize: series.symbolSize || 4,
                color,
                seriesIndex: seriesIndex,
                columnName: columnName,
                batchId: apiData.batchId || `Batch_${seriesIndex + 1}`,
                batchStartTime: startTime,
                batchEndTime: endTime,
                system: apiData.system || '',
                yAxis: apiData.yAxis || columnName
              });
            }
          });
        } else {
          // New structure: columnName -> phaseName -> series[]
          const phaseNames = Object.keys(columnData);

          phaseNames.forEach(phaseName => {
            const phaseData = columnData[phaseName];
            if (phaseData?.series && Array.isArray(phaseData.series)) {
              phaseData.series.forEach((series: any, seriesIndex: number) => {
                if (series?.data && Array.isArray(series.data) && series.data.length > 0) {
                  const seriesName = series.name || `${columnName} - ${phaseName} - Series ${seriesIndex + 1}`;
                  const color = series.color || series.lineStyle?.color || '#52c41a';
                  const width = typeof series.lineStyle?.width === 'number' ? series.lineStyle.width : 2;

                  const timestamps = series.data.map((point: any) => {
                    if (Array.isArray(point) && point.length >= 2) {
                      return new Date(point[0]).getTime();
                    }
                    return Date.now();
                  });
                  const startTime = Math.min(...timestamps);
                  const endTime = Math.max(...timestamps);

                  result.push({
                    name: seriesName,
                    data: series.data,
                    groupValue: `${phaseName} - ${series.name || 'Series'}`,
                    originalColumn: columnName,
                    lineStyle: { width, color },
                    symbolSize: series.symbolSize || 4,
                    color,
                    seriesIndex: seriesIndex,
                    columnName: columnName,
                    batchId: apiData.batchId || `Batch_${seriesIndex + 1}`,
                    batchStartTime: startTime,
                    batchEndTime: endTime,
                    system: apiData.system || '',
                    yAxis: apiData.yAxis || columnName
                  });
                }
              });
            }
          });
        }
      });
    } else if (apiData.series && Array.isArray(apiData.series)) {
      const columnName = configuration.basic?.selectedColumns?.headers?.[0] || 'Temperature';
      apiData.series.forEach((series: any, seriesIndex: number) => {
        if (series?.data && Array.isArray(series.data) && series.data.length > 0) {
          const seriesName = series.name || `Phase ${seriesIndex + 1}`;
          const color = series.color || series.lineStyle?.color || '#52c41a';
          const width = typeof series.lineStyle?.width === 'number' ? series.lineStyle.width : 2;

          const timestamps = series.data.map((point: any) => {
            if (Array.isArray(point) && point.length >= 2) {
              return new Date(point[0]).getTime();
            }
            return Date.now();
          });
          const startTime = Math.min(...timestamps);
          const endTime = Math.max(...timestamps);

          result.push({
            name: seriesName,
            data: series.data,
            groupValue: series.name || null,
            originalColumn: columnName,
            lineStyle: { width, color },
            symbolSize: series.symbolSize || 4,
            color,
            seriesIndex: seriesIndex,
            columnName: columnName,
            batchId: apiData.batchId || `Batch_${seriesIndex + 1}`,
            batchStartTime: startTime,
            batchEndTime: endTime,
            system: apiData.system || '',
            yAxis: apiData.yAxis || columnName
          });
        }
      });
    } else if (apiData.data && Array.isArray(apiData.data)) {
      const columnName = configuration.basic?.selectedColumns?.headers?.[0] || 'Temperature';
      const seriesName = `${columnName} - ${apiData.batchId || 'Unknown'}`;

      const timestamps = apiData.data.map((point: any) => {
        if (Array.isArray(point) && point.length >= 2) {
          return new Date(point[0]).getTime();
        }
        return Date.now();
      });
      const startTime = Math.min(...timestamps);
      const endTime = Math.max(...timestamps);

      result.push({
        name: seriesName,
        data: apiData.data,
        groupValue: null,
        originalColumn: columnName,
        lineStyle: { width: 2, color: '#52c41a' },
        symbolSize: 4,
        color: '#52c41a',
        seriesIndex: 0,
        columnName: columnName,
        batchId: apiData.batchId || 'Unknown',
        batchStartTime: startTime,
        batchEndTime: endTime,
        system: apiData.system || '',
        yAxis: apiData.yAxis || columnName
      });
    }

    return result;
  }, [hasApiData, configuration?.apiData, configuration?.basic?.selectedColumns?.headers]);

  const createPhaseIdentificationOption = useMemo(() => {
    if (!plotData || plotData.length === 0) {
      return {
        series: [],
        xAxis: { type: 'time' },
        yAxis: { type: 'value' },
        hasData: false
      };
    }

    const xAxisColumn = 'step_id'; // Fixed X-axis to step_id
    const xAxisType = 'value'; // step_id is a value type

    const seriesByColumn = plotData.reduce((acc: any, series: any) => {
      const columnName = series.columnName;
      if (!acc[columnName]) {
        acc[columnName] = [];
      }
      acc[columnName].push(series);
      return acc;
    }, {});

    const columnNames = Object.keys(seriesByColumn);
    const numColumns = columnNames.length;
    const grids: any[] = [];
    const xAxes: any[] = [];
    const yAxes: any[] = [];
    const series: any[] = [];

    columnNames.forEach((columnName, columnIndex) => {
      const columnSeries = seriesByColumn[columnName];

      // Calculate Y-axis range for this column
      let min = Infinity;
      let max = -Infinity;

      columnSeries.forEach((s: any) => {
        if (s.data && Array.isArray(s.data)) {
          s.data.forEach((point: any) => {
            if (point && Array.isArray(point) && typeof point[1] === 'number') {
              min = Math.min(min, point[1]);
              max = Math.max(max, point[1]);
            }
          });
        }
      });

      const yAxisRange = min !== Infinity && max !== -Infinity ? {
        min: min - (max - min) * 0.05,
        max: max + (max - min) * 0.05
      } : null;

      // Calculate grid position
      const gridHeightPercent = Math.floor(80 / numColumns);
      const topPercent = 10 + (columnIndex * (gridHeightPercent + 5));

      // Create grid for this column
      grids.push({
        left: isFullScreen ? '3%' : '60px',
        right: isFullScreen ? '3%' : '40px',
        top: `${topPercent}%`,
        height: `${gridHeightPercent}%`,
        containLabel: true
      });

      // Create X-axis for this column
      xAxes.push({
        type: xAxisType,
        gridIndex: columnIndex,
        name: columnIndex === numColumns - 1 ? xAxisColumn : '', // Only show name on bottom chart
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            width: 1,
            type: 'solid'
          }
        },
        axisLine: {
          show: true,
          lineStyle: { color: '#666' }
        },
        axisTick: { show: true },
        axisLabel: {
          show: columnIndex === numColumns - 1, // Only show labels on bottom chart
          fontSize: 11,
          color: '#666'
        }
      });

      // Create Y-axis for this column
      yAxes.push({
        type: 'value',
        gridIndex: columnIndex,
        name: columnName,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        scale: false,
        min: yAxisRange?.min,
        max: yAxisRange?.max,
        axisLabel: {
          formatter: function (value: any) {
            return typeof value === 'number' ? value.toFixed(2) : value;
          }
        }
      });

      // Add series for this column
      columnSeries.forEach((s: any) => {
        series.push({
          name: s.name,
          type: 'line',
          xAxisIndex: columnIndex,
          yAxisIndex: columnIndex,
          data: s.data || [],
          smooth: false,
          symbol: 'circle',
          symbolSize: s.symbolSize || 4,
          lineStyle: {
            width: s.lineStyle?.width || 2,
            color: s.color || s.lineStyle?.color || '#1f77b4'
          },
          itemStyle: {
            color: s.color || s.lineStyle?.color || '#1f77b4'
          },
          emphasis: {
            focus: 'series'
          }
        });
      });
    });

    const chartOption = {
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' },
        appendToBody: true,
        className: 'phase-identification-tooltip',
        extraCssText: 'z-index: 9999; position: fixed;',
        formatter: function(params: any) {
          if (!params || params.length === 0) return '';

          const param = params[0];
          const seriesData = plotData.find(p => p.name === param.seriesName);
          const featureName = seriesData?.columnName || 'Unknown';

          // Extract phase name from groupValue (format: "phaseName - seriesName")
          const groupValue = seriesData?.groupValue || '';
          const phaseName = groupValue.includes(' - ')
            ? groupValue.split(' - ')[0]
            : (param.seriesName || 'Unknown Phase');

          return `
            <div style="
              background: linear-gradient(135deg, #52c41a15, #52c41a05);
              border: 1px solid #52c41a40;
              border-radius: 8px;
              padding: 12px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            ">
              <div style="
                font-weight: 600;
                font-size: 14px;
                color: #52c41a;
                margin-bottom: 8px;
                display: flex;
                align-items: center;
                gap: 6px;
              ">
                <span style="color: #333;">Phase:</span> ${phaseName}
              </div>

             <div style="
               display: grid;
               grid-template-columns: auto 1fr;
               gap: 4px 8px;
               font-size: 12px;
               color: #666;
               margin-bottom: 8px;
             ">
               <span style="font-weight: 500;">📊 Feature:</span>
               <span>${featureName}</span>
             </div>

             <div style="
               border-top: 1px solid #f0f0f0;
               padding-top: 8px;
               font-size: 12px;
             ">
               <div style="
                 display: flex;
                 justify-content: space-between;
                 align-items: center;
                 padding: 2px 0;
               ">
                 <span style="
                   display: flex;
                   align-items: center;
                   gap: 6px;
                   color: #333;
                   font-weight: 500;
                 ">
                   <span style="
                     display: inline-block;
                     width: 8px;
                     height: 8px;
                     border-radius: 50%;
                     background: ${param.color};
                   "></span>
                   ${featureName}
                 </span>
                 <span style="
                   font-weight: 600;
                   color: #1890ff;
                 ">
                   ${Array.isArray(param.value) ? param.value[1]?.toFixed(2) : param.value?.toFixed(2)}
                 </span>
               </div>
             </div>
           </div>
         `;
        }
      },
      legend: {
        data: plotData.map(s => s.name),
        top: isFullScreen ? '0%' : 5,
        type: 'scroll',
        itemGap: 8,
        textStyle: { fontSize: 11 }
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: Array.from({ length: numColumns }, (_, i) => i),
          yAxisIndex: [],
          filterMode: 'filter'
        },
        {
          type: 'slider',
          xAxisIndex: Array.from({ length: numColumns }, (_, i) => i),
          yAxisIndex: [],
          filterMode: 'filter',
          bottom: 20
        }
      ],
      grid: grids,
      xAxis: xAxes,
      yAxis: yAxes,
      series: series,
      hasData: true
    };

    return chartOption;
  }, [plotData, isFullScreen, configuration?.basic?.selectedColumns?.headers]); // Removed xAxisColumn dependency since it's now fixed

  const handleDownloadPDF = async () => {
    if (!chartWrapperRef.current) return;
    const canvas = await html2canvas(chartWrapperRef.current, { useCORS: true });
    const imgData = canvas.toDataURL("image/png");
    const pdf = new jsPDF({
      orientation: "landscape",
      unit: "px",
      format: [canvas.width, canvas.height],
    });
    pdf.addImage(imgData, "PNG", 0, 0, canvas.width, canvas.height);
    pdf.save("phase-identification-chart.pdf");
  };

  // Show error state if there's an error (check this BEFORE configuration check)
  if (hasError) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full"
        style={{ minHeight: '300px' }}
      >
        <Empty
          image={<BranchesOutlined style={{ fontSize: '48px', color: '#ff4d4f' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-red-600 mb-2">
                Error Loading Phase Comparison Data
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                {errorMessage || 'Failed to load phase comparison data. Please try again.'}
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={() => onOpenConfiguration && onOpenConfiguration()}
              >
                Retry Configuration
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  // Render empty state with configuration prompt when panel is not configured
  if (!configuration || !isConfigured) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full w-full"
        style={{ minHeight: '300px', width: '100%' }}
      >
        <Empty
          image={<BranchesOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-gray-700 mb-2">
                {!configuration ? 'Phase Comparison Panel Not Configured' : 'Configuration Incomplete'}
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                {!configuration
                  ? 'Configure this panel to compare and analyze process phases in your PLC data. First ensure you have batch comparison data loaded.'
                  : 'Please complete the configuration by selecting a batch and phase parameters'
                }
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
                style={{
                  background: '#52c41a',
                  borderColor: '#52c41a',
                  boxShadow: '0 2px 4px rgba(82, 196, 26, 0.2)'
                }}
              >
                Configure Panel
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  // Show error state if API data has error
  if (hasApiData && configuration?.apiData?.error) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full"
        style={{ minHeight: '300px' }}
      >
        <Empty
          image={<BranchesOutlined style={{ fontSize: '48px', color: '#ff4d4f' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-red-600 mb-2">
                Phase Comparison Error
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                {configuration.apiData.error}
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
                style={{
                  background: '#52c41a',
                  borderColor: '#52c41a'
                }}
              >
                Reconfigure Panel
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  // Show empty state if no plot data
  if (isConfigured && plotData.length === 0) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full"
        style={{ minHeight: '300px' }}
      >
        <Empty
          image={<BranchesOutlined style={{ fontSize: '48px', color: '#52c41a' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-gray-800 mb-2">
                No Phase Data Available
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                No phase comparison data found for the selected batches. Try selecting different batches or check the parameters.
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
                style={{
                  background: '#52c41a',
                  borderColor: '#52c41a'
                }}
              >
                Adjust Parameters
              </Button>
            </div>
          }
        />
      </div>
    );
  }



  // Get display information for panel header
  const uniqueColumns = plotData.map(s => s.columnName).filter((value, index, self) => self.indexOf(value) === index);
  const displayedColumnsCount = uniqueColumns.length;
  const totalBatches = plotData.length; // Count total batches instead of series
  const selectedPhases = configuration?.apiData?.selectedPhases || [];
  const phaseInfo = selectedPhases.length > 0 ? ` - Phases: ${selectedPhases.join(', ')}` : '';

  return (
    <div className={`phase-identification-panel ${isFullScreen ? 'h-full' : 'h-[95%]'}`} ref={containerRef} style={{ position: 'relative' }}>
      <div className={`flex justify-between items-center ${isFullScreen ? 'mb-0 px-2 pt-0' : 'mb-1 px-3 pt-1'}`}>
        <h3 className="text-base font-medium" style={{ color: '#222' }}>
          Phase Comparison ({displayedColumnsCount} columns, {totalBatches} batches){phaseInfo}
        </h3>
        <div className="flex gap-2">
          <Button onClick={handleDownloadPDF} icon={<BranchesOutlined />}>
            Download PDF
          </Button>
        </div>
      </div>
      <div className={`${isFullScreen ? 'p-0 h-[calc(100%-40px)]' : 'p-2 h-[calc(100%-50px)]'} overflow-auto`}>
        <div
          ref={chartWrapperRef}
          style={{
            height: '100%',
            minHeight: isFullScreen ? '100%' : '400px'
          }}
        >
          {containerSize.width > 0 && containerSize.height > 0 && isChartReady && !isLoading ? (
            <ReactECharts
              key={`phase-identification-${chartUpdateTrigger}`}
              ref={echartsRef}
              option={createPhaseIdentificationOption}
              style={{
                width: '100%',
                height: '100%',
                ...(isFullScreen ? {} : { minHeight: '400px' })
              }}
              opts={{ renderer: 'canvas' }}
              notMerge={true}
            />
          ) : (
            <div style={{
              width: '100%',
              height: isFullScreen ? '100%' : '400px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Spin size="large" tip="Loading phase comparison chart..." />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PhaseIdentificationPanel; 